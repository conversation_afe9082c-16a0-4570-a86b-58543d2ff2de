function fun(val)
{
    if(val=="0")
	{
		return "PMSM08";
	}
	else if(val=="1")
	{
		return "PMSM08-G";
	}
	else if(val=="2")
	{
		return "BCP01";
	}
	else if(val=="3")
	{
		return "BCP02-110V";
	}
	else if(val=="4")
	{
		return "BCP02-330V";
	}
	else if(val=="5")
	{
		return "BCP02-600V";
	}
	else if(val=="6")
	{
		return "PMSM13";
	}
	else if(val=="7")
	{
		return "PMSM11G-4kW";
	}
	else if(val=="8")
	{
		return "PMSM11G-1.3kW";
	}
	else if(val=="10")
	{
		return "PVPB04";
	}
	else if(val=="11")
	{
		return "ACIP01";
	}
	else if(val=="12")
	{
		return "PVPB07";
	}
	else if(val=="13")
	{
		return "PVPB08";
	}
	else
	{
		return val;
	}
}

