<?xml version="1.0" encoding="UTF-8"?>
<IO>
<!-- 参数设置 -->
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1"  COLUMN_COUNT="4" GROUP_NAME="参数设置" >PVPB_DEVICE_01</TITLE>

    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="013" WRITE_OFFSET="013" BYTE="1" BIT="0" WIDGET_NAME="通信ID设置" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="023" WRITE_OFFSET="023" BYTE="1" BIT="0" WIDGET_NAME="CAN_X通讯地址" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="024" WRITE_OFFSET="024" BYTE="1" BIT="0" WIDGET_NAME="CAN_Y通讯地址" />

    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="014" WRITE_OFFSET="014" BYTE="4" BIT="0" WIDGET_NAME="RS485波特率(bps)" READ_SCRIPT="通用_解析_波特率.js" WRITE_SCRIPT="通用_设置_波特率.js" ENUM_SCRIPT="通用_枚举_波特率.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="018" WRITE_OFFSET="018" BYTE="1" BIT="0" WIDGET_NAME="RS485校验位" READ_SCRIPT="通用_解析_奇偶校验.js" WRITE_SCRIPT="通用_设置_奇偶校验.js" ENUM_SCRIPT="通用_枚举_奇偶校验.js"/>																						 		 		  				  					      	 
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="2" READ_OFFSET="019" WRITE_OFFSET="019" BYTE="4" BIT="0" WIDGET_NAME="CAN波特率(bps)" READ_SCRIPT="通用_解析_CAN波特率.js" WRITE_SCRIPT="通用_设置_CAN波特率.js" ENUM_SCRIPT="通用_枚举_CAN波特率.js"/>

    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="3" READ_OFFSET="009" WRITE_OFFSET="009" BYTE="4" BIT="0" WIDGET_NAME="设置时间" READ_SCRIPT="通用_时间戳_秒.js" />
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="025" WRITE_OFFSET="025" BYTE="2" BIT="0" WIDGET_NAME="缺省输出频率(Hz)" READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>


<!-- 电驱参数 -->                                                                                                                                                                  																						 		 		  				  					      	 
<TITLE GROUP_ROW="1" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1"  COLUMN_COUNT="4" GROUP_NAME="电驱参数" >PVPB_DEVICE_02</TITLE> 
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1"  WIDGET_EDIT="1" READ_OFFSET="137" WRITE_OFFSET="137" BYTE="2" BIT="0" WIDGET_NAME="参数模板" READ_SCRIPT="通用_电驱索引读.js" WRITE_SCRIPT="通用_电驱索引写.js" ENUM_SCRIPT="通用_电驱索引.js"/>
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1"  WIDGET_EDIT="1" READ_OFFSET="139" WRITE_OFFSET="139" BYTE="2" BIT="0" WIDGET_NAME="P001-控制方式" READ_SCRIPT="通用_控制方式_读.js" WRITE_SCRIPT="通用_控制方式_写.js" ENUM_SCRIPT="通用_控制方式_枚举.js" />
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1"  WIDGET_EDIT="1" READ_OFFSET="141" WRITE_OFFSET="141" BYTE="2" BIT="0" WIDGET_NAME="P009-运转方向" READ_SCRIPT="通用_运转_读.js" WRITE_SCRIPT="通用_运转_写.js" ENUM_SCRIPT="通用_运转_枚举.js" />   
    <CHILD WIDGET_TYPE="PARAM"      WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1"  WIDGET_EDIT="1" READ_OFFSET="413" WRITE_OFFSET="413" BYTE="2" BIT="0" WIDGET_NAME="P010-最大频率(Hz)" READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM"      WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1"  WIDGET_EDIT="1" READ_OFFSET="143" WRITE_OFFSET="143" BYTE="2" BIT="0" WIDGET_NAME="P012-上限频率(Hz)" READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM"      WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1"  WIDGET_EDIT="1" READ_OFFSET="145" WRITE_OFFSET="145" BYTE="2" BIT="0" WIDGET_NAME="P014-下限频率(Hz)" READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM"      WIDGET_ROW="2"  WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="147" WRITE_OFFSET="147" BYTE="2" BIT="0" WIDGET_NAME="P015-载波频率(kHz)" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM"      WIDGET_ROW="2"  WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="149" WRITE_OFFSET="149" BYTE="2" BIT="0" WIDGET_NAME="P017-加速时间(s)" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM"      WIDGET_ROW="2"  WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="151" WRITE_OFFSET="151" BYTE="2" BIT="0" WIDGET_NAME="P018-减速时间(s)" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="3"  WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="153" WRITE_OFFSET="153" BYTE="2" BIT="0" WIDGET_NAME="P100-电机类型" 		READ_SCRIPT="通用_电机类型_读.js" 	WRITE_SCRIPT="通用_电机类型_写.js" ENUM_SCRIPT="通用_电机类型_枚举.js" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="3"  WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="155" WRITE_OFFSET="155" BYTE="2" BIT="0" WIDGET_NAME="P101-电机额定功率(kW)" 	READ_SCRIPT="通用_除1000_小数3.js"  WRITE_SCRIPT="通用_乘1000.js" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="3"  WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="157" WRITE_OFFSET="157" BYTE="2" BIT="0" WIDGET_NAME="P102-电机额定电压(V)" 	/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="4"  WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="159" WRITE_OFFSET="159" BYTE="2" BIT="0" WIDGET_NAME="P103-电机额定电流(A)" 	READ_SCRIPT="通用_除100_小数2.js" 	WRITE_SCRIPT="通用_乘100.js" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="4"  WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="161" WRITE_OFFSET="161" BYTE="2" BIT="0" WIDGET_NAME="P104-电机额定频率(Hz)" 	READ_SCRIPT="通用_除100_小数2.js" 	WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="4"  WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="163" WRITE_OFFSET="163" BYTE="2" BIT="0" WIDGET_NAME="P105-电机额定转速(RPM)" 	/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="5"  WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="165" WRITE_OFFSET="165" BYTE="2" BIT="0" WIDGET_NAME="P116-定子电阻(Ω)" 	READ_SCRIPT="通用_除1000_小数3.js" 	WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="5"  WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="167" WRITE_OFFSET="167" BYTE="2" BIT="0" WIDGET_NAME="P117-D轴电感(mH)" 	READ_SCRIPT="通用_除100_小数2.js" 	WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="5"  WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="169" WRITE_OFFSET="169" BYTE="2" BIT="0" WIDGET_NAME="P118-Q轴电感(mH)" 	READ_SCRIPT="通用_除100_小数2.js" 	WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="6"  WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="171" WRITE_OFFSET="171" BYTE="2" BIT="0" WIDGET_NAME="P120-反电动势(V)" 	READ_SCRIPT="通用_除10_小数1.js" 	WRITE_SCRIPT="通用_乘10.js"/>	
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="6"  WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="173" WRITE_OFFSET="173" BYTE="2" BIT="0" WIDGET_NAME="P137-调谐选择" 		READ_SCRIPT="通用_调谐选择_读.js" 	WRITE_SCRIPT="通用_调谐选择_写.js" ENUM_SCRIPT="通用_调谐选择_枚举.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="6"  WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="175" WRITE_OFFSET="175" BYTE="2" BIT="0" WIDGET_NAME="P200-速度环比例增益1" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="7"  WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="177" WRITE_OFFSET="177" BYTE="2" BIT="0" WIDGET_NAME="P201-速度环积分时间1" READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="7"  WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="179" WRITE_OFFSET="179" BYTE="2" BIT="0" WIDGET_NAME="P202-切换频率1(Hz)" 		READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="7"  WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="181" WRITE_OFFSET="181" BYTE="2" BIT="0" WIDGET_NAME="P203-速度环比例增益2" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="8"  WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="183" WRITE_OFFSET="183" BYTE="2" BIT="0" WIDGET_NAME="P204-速度环积分时间2" READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="8"  WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="185" WRITE_OFFSET="185" BYTE="2" BIT="0" WIDGET_NAME="P205-切换频率2(Hz)" 		READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="8"  WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="187" WRITE_OFFSET="187" BYTE="2" BIT="0" WIDGET_NAME="P206-转差补偿系数" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="9"  WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="189" WRITE_OFFSET="189" BYTE="2" BIT="0" WIDGET_NAME="P207-滤波时间常数" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="9"  WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="191" WRITE_OFFSET="191" BYTE="2" BIT="0" WIDGET_NAME="P208-矢量控制过励磁增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="9"  WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="193" WRITE_OFFSET="193" BYTE="2" BIT="0" WIDGET_NAME="P209-驱动转矩上限源" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="10" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="195" WRITE_OFFSET="195" BYTE="2" BIT="0" WIDGET_NAME="P210-驱动转矩上限(%)" 	READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="10" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="197" WRITE_OFFSET="197" BYTE="2" BIT="0" WIDGET_NAME="P211-制动转矩上限源" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="10" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="199" WRITE_OFFSET="199" BYTE="2" BIT="0" WIDGET_NAME="P212-制动转矩上限(%)" 	READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="11" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="201" WRITE_OFFSET="201" BYTE="2" BIT="0" WIDGET_NAME="P213-M轴电流环Kp" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="11" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="203" WRITE_OFFSET="203" BYTE="2" BIT="0" WIDGET_NAME="P214-M轴电流环Ki" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="11" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="205" WRITE_OFFSET="205" BYTE="2" BIT="0" WIDGET_NAME="P215-T轴电流环Kp" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="12" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="207" WRITE_OFFSET="207" BYTE="2" BIT="0" WIDGET_NAME="P216-T轴电流环Ki" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="12" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="209" WRITE_OFFSET="209" BYTE="2" BIT="0" WIDGET_NAME="P217-速度环积分属性" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="12" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="211" WRITE_OFFSET="211" BYTE="2" BIT="0" WIDGET_NAME="P218-同步机弱磁模式" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="13" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="213" WRITE_OFFSET="213" BYTE="2" BIT="0" WIDGET_NAME="P219-同步机弱磁系数" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="13" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="215" WRITE_OFFSET="215" BYTE="2" BIT="0" WIDGET_NAME="P220-最大弱磁电流" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="13" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="217" WRITE_OFFSET="217" BYTE="2" BIT="0" WIDGET_NAME="P221-弱磁自动调谐系数" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="14" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="219" WRITE_OFFSET="219" BYTE="2" BIT="0" WIDGET_NAME="P222-弱磁积分倍数" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="14" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="221" WRITE_OFFSET="221" BYTE="2" BIT="0" WIDGET_NAME="P223-弱磁深度" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="14" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="223" WRITE_OFFSET="223" BYTE="2" BIT="0" WIDGET_NAME="P224-同步机弱磁系数(%)" />
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="15" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="225" WRITE_OFFSET="225" BYTE="2" BIT="0" WIDGET_NAME="P225-初始位置是否检测"  READ_SCRIPT="通用_初始位置检测_读.js"  WRITE_SCRIPT="通用_初始位置检测_写.js" ENUM_SCRIPT="通用_初始位置检测_枚举.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="15" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="227" WRITE_OFFSET="227" BYTE="2" BIT="0" WIDGET_NAME="P226-速度环模式选择" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="15" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="229" WRITE_OFFSET="229" BYTE="2" BIT="0" WIDGET_NAME="P227-最大出力调整系数" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="16" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="231" WRITE_OFFSET="231" BYTE="2" BIT="0" WIDGET_NAME="P228-跟据母线电压对频率限幅使能" />	
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="16" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="233" WRITE_OFFSET="233" BYTE="2" BIT="0" WIDGET_NAME="P229-前馈补偿模式" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="16" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="235" WRITE_OFFSET="235" BYTE="2" BIT="0" WIDGET_NAME="P230-调谐时电流环KP" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="17" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="237" WRITE_OFFSET="237" BYTE="2" BIT="0" WIDGET_NAME="P231-调谐时电流环KI" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="17" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="239" WRITE_OFFSET="239" BYTE="2" BIT="0" WIDGET_NAME="P232-Z信号校正使能" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="17" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="241" WRITE_OFFSET="241" BYTE="2" BIT="0" WIDGET_NAME="P233-同步机SVC速度滤波级别" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="18" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="243" WRITE_OFFSET="243" BYTE="2" BIT="0" WIDGET_NAME="P234-同步机SVC速度估算比例增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="18" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="245" WRITE_OFFSET="245" BYTE="2" BIT="0" WIDGET_NAME="P235-同步机SVC速度估算积分增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="18" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="247" WRITE_OFFSET="247" BYTE="2" BIT="0" WIDGET_NAME="P236-同步机SVC初始励磁电流限幅" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="19" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="249" WRITE_OFFSET="249" BYTE="2" BIT="0" WIDGET_NAME="P237-同步机SVC最低载波频率(kHz)" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="19" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="251" WRITE_OFFSET="251" BYTE="2" BIT="0" WIDGET_NAME="P238-低频运行方式" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="19" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="253" WRITE_OFFSET="253" BYTE="2" BIT="0" WIDGET_NAME="P239-低频生效" READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="20" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="255" WRITE_OFFSET="255" BYTE="2" BIT="0" WIDGET_NAME="P240-低频频率步长" READ_SCRIPT="通用_除10000_小数4.js" WRITE_SCRIPT="通用_乘10000.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="20" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="257" WRITE_OFFSET="257" BYTE="2" BIT="0" WIDGET_NAME="P241-低频制动电流(%)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="20" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="259" WRITE_OFFSET="259" BYTE="2" BIT="0" WIDGET_NAME="P242-同步机SVC速度跟踪" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="21" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="261" WRITE_OFFSET="261" BYTE="2" BIT="0" WIDGET_NAME="P243-零伺服使能" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="21" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="263" WRITE_OFFSET="263" BYTE="2" BIT="0" WIDGET_NAME="P244-切换频率(Hz)" 		READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js" />	
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="21" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="265" WRITE_OFFSET="265" BYTE="2" BIT="0" WIDGET_NAME="P245-零伺服速度环比例增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="22" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="267" WRITE_OFFSET="267" BYTE="2" BIT="0" WIDGET_NAME="P246-零伺服速度环积分时间" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="22" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="269" WRITE_OFFSET="269" BYTE="2" BIT="0" WIDGET_NAME="P247-停机禁止反转" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="22" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="271" WRITE_OFFSET="271" BYTE="2" BIT="0" WIDGET_NAME="P248-停机角度" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="23" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="273" WRITE_OFFSET="273" BYTE="2" BIT="0" WIDGET_NAME="P249-在线调谐使能" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="23" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="275" WRITE_OFFSET="275" BYTE="2" BIT="0" WIDGET_NAME="P250-在线反电动势辨识" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="23" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="277" WRITE_OFFSET="277" BYTE="2" BIT="0" WIDGET_NAME="P251-初始位置补偿角度" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="24" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="279" WRITE_OFFSET="279" BYTE="2" BIT="0" WIDGET_NAME="P300-VF曲线设定" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="24" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="281" WRITE_OFFSET="281" BYTE="2" BIT="0" WIDGET_NAME="P301-转矩提升(%)" 			READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="24" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="283" WRITE_OFFSET="283" BYTE="2" BIT="0" WIDGET_NAME="P302-转矩提升截止频率(Hz)" 	READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="25" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="285" WRITE_OFFSET="285" BYTE="2" BIT="0" WIDGET_NAME="P303-多点VF频率点1(Hz)" 		READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="25" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="287" WRITE_OFFSET="287" BYTE="2" BIT="0" WIDGET_NAME="P304-多点VF电压点1(V)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="25" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="289" WRITE_OFFSET="289" BYTE="2" BIT="0" WIDGET_NAME="P305-多点VF频率点2(Hz)" 		READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="26" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="291" WRITE_OFFSET="291" BYTE="2" BIT="0" WIDGET_NAME="P306-多点VF电压点2(V)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="26" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="293" WRITE_OFFSET="293" BYTE="2" BIT="0" WIDGET_NAME="P307-多点VF频率点3(Hz)" 		READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="26" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="295" WRITE_OFFSET="295" BYTE="2" BIT="0" WIDGET_NAME="P308-多点VF电压点3(V)" />	
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="27" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="297" WRITE_OFFSET="297" BYTE="2" BIT="0" WIDGET_NAME="P309-转差补偿系数(%)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="27" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="299" WRITE_OFFSET="299" BYTE="2" BIT="0" WIDGET_NAME="P310-VF过励磁增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="27" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="301" WRITE_OFFSET="301" BYTE="2" BIT="0" WIDGET_NAME="P311-VF振荡抑制增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="28" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="303" WRITE_OFFSET="303" BYTE="2" BIT="0" WIDGET_NAME="P312-VF振荡抑制增益模式" /> 
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="28" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="305" WRITE_OFFSET="305" BYTE="2" BIT="0" WIDGET_NAME="P313-VF分离的电压源" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="28" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="307" WRITE_OFFSET="307" BYTE="2" BIT="0" WIDGET_NAME="P314-VF分离的电压数字设定(V)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="29" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="309" WRITE_OFFSET="309" BYTE="2" BIT="0" WIDGET_NAME="P315-VF分离的电压加速时间(s)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="29" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="311" WRITE_OFFSET="311" BYTE="2" BIT="0" WIDGET_NAME="P316-VF分离的电压减速时间(s)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="29" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="313" WRITE_OFFSET="313" BYTE="2" BIT="0" WIDGET_NAME="P317-VF分离停机方式选择" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="30" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="315" WRITE_OFFSET="315" BYTE="2" BIT="0" WIDGET_NAME="P318-VF过流失速动作电流(%)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="30" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="317" WRITE_OFFSET="317" BYTE="2" BIT="0" WIDGET_NAME="P319-过流失速使能" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="30" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="319" WRITE_OFFSET="319" BYTE="2" BIT="0" WIDGET_NAME="P320-过流失速抑制增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="31" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="321" WRITE_OFFSET="321" BYTE="2" BIT="0" WIDGET_NAME="P321-倍速过流失速动作电流补偿系数(%)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="31" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="323" WRITE_OFFSET="323" BYTE="2" BIT="0" WIDGET_NAME="P322-VF过压失速动作电压(V)" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="31" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="325" WRITE_OFFSET="325" BYTE="2" BIT="0" WIDGET_NAME="P323-VF过压失速使能" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="32" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="327" WRITE_OFFSET="327" BYTE="2" BIT="0" WIDGET_NAME="P324-VF过压失速抑制频率增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="32" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="329" WRITE_OFFSET="329" BYTE="2" BIT="0" WIDGET_NAME="P325-VF过压失速抑制电压增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="32" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="331" WRITE_OFFSET="331" BYTE="2" BIT="0" WIDGET_NAME="P326-过压失速最大上升频率限制(Hz)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="33" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="333" WRITE_OFFSET="333" BYTE="2" BIT="0" WIDGET_NAME="P327-转差补偿时间常数" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="33" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="335" WRITE_OFFSET="335" BYTE="2" BIT="0" WIDGET_NAME="P328-自动升频使能" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="33" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="337" WRITE_OFFSET="337" BYTE="2" BIT="0" WIDGET_NAME="P329-最小电动力矩电流" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="34" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="339" WRITE_OFFSET="339" BYTE="2" BIT="0" WIDGET_NAME="P330-最小电动力矩电流" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="34" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="341" WRITE_OFFSET="341" BYTE="2" BIT="0" WIDGET_NAME="P331-自动升频KP" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="34" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="343" WRITE_OFFSET="343" BYTE="2" BIT="0" WIDGET_NAME="P332-自动升频KI" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="35" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="345" WRITE_OFFSET="345" BYTE="2" BIT="0" WIDGET_NAME="P333-在线转矩补偿增益" />
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="35" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="347" WRITE_OFFSET="347" BYTE="2" BIT="0" WIDGET_NAME="P600-启动方式" 				    READ_SCRIPT="通用_启动方式_读.js"  WRITE_SCRIPT="通用_启动方式_写.js" ENUM_SCRIPT="通用_启动方式_枚举.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="35" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="349" WRITE_OFFSET="349" BYTE="2" BIT="0" WIDGET_NAME="P601-转速跟踪模式" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="36" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="351" WRITE_OFFSET="351" BYTE="2" BIT="0" WIDGET_NAME="P602-转速跟踪快慢" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="36" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="353" WRITE_OFFSET="353" BYTE="2" BIT="0" WIDGET_NAME="P603-启动频率(Hz)" 			    READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="36" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="355" WRITE_OFFSET="355" BYTE="2" BIT="0" WIDGET_NAME="P604-启动频率保持时间(s)" 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="37" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="357" WRITE_OFFSET="357" BYTE="2" BIT="0" WIDGET_NAME="P605-启动直流制动电流(%)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="37" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="359" WRITE_OFFSET="359" BYTE="2" BIT="0" WIDGET_NAME="P606-启动直流制动时间(s)" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="37" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="361" WRITE_OFFSET="361" BYTE="2" BIT="0" WIDGET_NAME="P607-加减速方式" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="38" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="363" WRITE_OFFSET="363" BYTE="2" BIT="0" WIDGET_NAME="P608-S曲线开始段时间比例(%)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="38" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="365" WRITE_OFFSET="365" BYTE="2" BIT="0" WIDGET_NAME="P609-S曲线结束段时间比例(%)" />
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="38" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="367" WRITE_OFFSET="367" BYTE="2" BIT="0" WIDGET_NAME="P610-停机方式"     				READ_SCRIPT="通用_停机方式_读.js"  WRITE_SCRIPT="通用_停机方式_写.js" ENUM_SCRIPT="通用_停机方式_枚举.js" />/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="39" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="369" WRITE_OFFSET="369" BYTE="2" BIT="0" WIDGET_NAME="P611-停机直流制动起始频率(Hz)" 	READ_SCRIPT="通用_除100_小数2.js" 	WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="39" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="371" WRITE_OFFSET="371" BYTE="2" BIT="0" WIDGET_NAME="P612-停机直流制动等待时间(s)" 	READ_SCRIPT="通用_除10_小数1.js" 	WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="39" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="373" WRITE_OFFSET="373" BYTE="2" BIT="0" WIDGET_NAME="P613-停机直流制动电流(%)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="40" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="375" WRITE_OFFSET="375" BYTE="2" BIT="0" WIDGET_NAME="P614-停机直流制动时间(s)" 		READ_SCRIPT="通用_除10_小数1.js" 	WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="40" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="377" WRITE_OFFSET="377" BYTE="2" BIT="0" WIDGET_NAME="P615-制动使用率(%)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="40" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="379" WRITE_OFFSET="379" BYTE="2" BIT="0" WIDGET_NAME="P616-制动电阻开通时间" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="41" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="381" WRITE_OFFSET="381" BYTE="2" BIT="0" WIDGET_NAME="P617-转速跟踪闭环电流KP" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="41" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="383" WRITE_OFFSET="383" BYTE="2" BIT="0" WIDGET_NAME="P618-转速跟踪闭环电流KI" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="41" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="385" WRITE_OFFSET="385" BYTE="2" BIT="0" WIDGET_NAME="P619-转速跟踪电流大小" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="42" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="387" WRITE_OFFSET="387" BYTE="2" BIT="0" WIDGET_NAME="P620-转速跟踪闭环电流下限定值" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="42" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="389" WRITE_OFFSET="389" BYTE="2" BIT="0" WIDGET_NAME="P621-转速跟踪电压上升时间" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="42" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="391" WRITE_OFFSET="391" BYTE="2" BIT="0" WIDGET_NAME="P622-最低输出频率" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="43" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="393" WRITE_OFFSET="393" BYTE="2" BIT="0" WIDGET_NAME="P900-电机过载保护选择" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="43" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="395" WRITE_OFFSET="395" BYTE="2" BIT="0" WIDGET_NAME="P901-电机过载保护增益" 		READ_SCRIPT="通用_除100_小数2.js" WRITE_SCRIPT="通用_乘100.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="43" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="397" WRITE_OFFSET="397" BYTE="2" BIT="0" WIDGET_NAME="P902-电机过载预警系数(%)" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="44" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="399" WRITE_OFFSET="399" BYTE="2" BIT="0" WIDGET_NAME="P903-过压失速增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="44" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="401" WRITE_OFFSET="401" BYTE="2" BIT="0" WIDGET_NAME="P904-过压设置点(V)" 			READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="44" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="403" WRITE_OFFSET="403" BYTE="2" BIT="0" WIDGET_NAME="P905-过流失速增益" />
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="45" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="405" WRITE_OFFSET="405" BYTE="2" BIT="0" WIDGET_NAME="P906-过流失速保护电流(%)" />
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="45" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="407" WRITE_OFFSET="407" BYTE="2" BIT="0" WIDGET_NAME="P907-对地短路保护选择" 	READ_SCRIPT="通用_对地短路保护选择_读.js"  WRITE_SCRIPT="通用_对地短路保护选择_写.js" ENUM_SCRIPT="通用_对地短路保护选择_枚举.js" />
    <CHILD WIDGET_TYPE="ENUM_PARAM" WIDGET_ROW="45" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="409" WRITE_OFFSET="409" BYTE="2" BIT="0" WIDGET_NAME="P913-输出缺相保护选择"  READ_SCRIPT="通用_输出缺相保护选择_读.js"  WRITE_SCRIPT="通用_输出缺相保护选择_写.js" ENUM_SCRIPT="通用_输出缺相保护选择_枚举.js"/>
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="46" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="411" WRITE_OFFSET="411" BYTE="2" BIT="0" WIDGET_NAME="P948-故障保护动作选择2" />  
    <CHILD WIDGET_TYPE="PARAM" 		WIDGET_ROW="46" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="1" WIDGET_DISP="2" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="415" WRITE_OFFSET="415" BYTE="2" BIT="0" WIDGET_NAME="PU01-Breakin自恢复" />   	
<!-- 校准参数 -->
<TITLE GROUP_ROW="2" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1"  COLUMN_COUNT="4" GROUP_NAME="校准参数 线性校准(默认:1.000)" >PVPB_DEVICE_04</TITLE>																		 		 		  				  					      	 

    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="073" WRITE_OFFSET="073" BYTE="2" BIT="0" WIDGET_NAME="R相输入电压" 	READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="075" WRITE_OFFSET="075" BYTE="2" BIT="0" WIDGET_NAME="S相输入电压" 	READ_SCRIPT="通用_除1000_小数3.js"    	WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="077" WRITE_OFFSET="077" BYTE="2" BIT="0" WIDGET_NAME="T相输入电压" 	READ_SCRIPT="通用_除1000_小数3.js"    	WRITE_SCRIPT="通用_乘1000.js"/>

    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="079" WRITE_OFFSET="079" BYTE="2" BIT="0" WIDGET_NAME="直流输入电压" 	READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>                                                                                                                                                                                                                                                                                                                                                                                               
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="081" WRITE_OFFSET="081" BYTE="2" BIT="0" WIDGET_NAME="母线电压"  	READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js" />
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="083" WRITE_OFFSET="083" BYTE="2" BIT="0" WIDGET_NAME="U相输出电流" 	READ_SCRIPT="通用_除1000_小数3.js" 		WRITE_SCRIPT="通用_乘1000.js"/> 																			 		 		  				  					      	                                                                                                                                                                                                     
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="085" WRITE_OFFSET="085" BYTE="2" BIT="0" WIDGET_NAME="V相输出电流" 	READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>

    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="087" WRITE_OFFSET="087" BYTE="2" BIT="0" WIDGET_NAME="W相输出电流" 	READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>																																	 
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="089" WRITE_OFFSET="089" BYTE="2" BIT="0" WIDGET_NAME="输出电压" 	READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="091" WRITE_OFFSET="091" BYTE="2" BIT="0" WIDGET_NAME="腔内温度" 	READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>                                                                                                                                                                                                                                                                                                                                                                                               

    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="093" WRITE_OFFSET="093" BYTE="2" BIT="0" WIDGET_NAME="腔内湿度" 	READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>																																	 
    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="095" WRITE_OFFSET="095" BYTE="2" BIT="0" WIDGET_NAME="母线电容温度"  READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="097" WRITE_OFFSET="097" BYTE="2" BIT="0" WIDGET_NAME="IGBT温度" 	READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>

    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="099" WRITE_OFFSET="099" BYTE="2" BIT="0" WIDGET_NAME="5V电压" 		READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="101" WRITE_OFFSET="101" BYTE="2" BIT="0" WIDGET_NAME="15V电压" 		READ_SCRIPT="通用_除1000_小数3.js"  	WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="103" WRITE_OFFSET="103" BYTE="2" BIT="0" WIDGET_NAME="MCU温度" 		READ_SCRIPT="通用_除1000_小数3.js"     WRITE_SCRIPT="通用_乘1000.js"/>

    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="105" WRITE_OFFSET="105" BYTE="2" BIT="0" WIDGET_NAME="3.3V电压" 	READ_SCRIPT="通用_除1000_小数3.js"     WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="107" WRITE_OFFSET="107" BYTE="2" BIT="0" WIDGET_NAME="DC电感温度" 	READ_SCRIPT="通用_除1000_小数3.js"     WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="06" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="109" WRITE_OFFSET="109" BYTE="2" BIT="0" WIDGET_NAME="AI模拟输入" 	READ_SCRIPT="通用_除1000_小数3.js"     WRITE_SCRIPT="通用_乘1000.js"/>

    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="0" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="413" WRITE_OFFSET="413" BYTE="1" BIT="0" WIDGET_NAME="预留" 	/>

</IO>
