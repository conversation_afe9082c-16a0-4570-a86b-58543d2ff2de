<?xml version="1.0" encoding="UTF-8" ?>
<IO>

<!-- -->
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" GROUP_NAME="运营统计" >PVPB_PARAMTER_WIFI</TITLE> 
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="3" READ_OFFSET="009" WRITE_OFFSET="009" BYTE="1" BIT="0" WIDGET_NAME="清除全部统计数据" />
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="013" WRITE_OFFSET="013" BYTE="4" BIT="0" WIDGET_NAME="AC380V输入总能耗(kWh)" 	READ_SCRIPT="通用_除1000_小数3.js"/>

    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="029" WRITE_OFFSET="029" BYTE="4" BIT="0" WIDGET_NAME="累计运行时间" 			READ_SCRIPT="通用_秒转分.js" />
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="033" WRITE_OFFSET="033" BYTE="4" BIT="0" WIDGET_NAME="AC380V累计工作时间" 		READ_SCRIPT="通用_秒转分.js" />
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="027" WRITE_OFFSET="027" BYTE="4" BIT="0" WIDGET_NAME="DC110V累计工作时间" 		READ_SCRIPT="通用_秒转分.js" />
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="041" WRITE_OFFSET="041" BYTE="4" BIT="0" WIDGET_NAME="AC380V上电次数" 	   />		
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="045" WRITE_OFFSET="045" BYTE="4" BIT="0" WIDGET_NAME="DC110V上电次数" 	   />
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="049" WRITE_OFFSET="049" BYTE="4" BIT="0" WIDGET_NAME="母线主继电器吸合次数" /> 
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="053" WRITE_OFFSET="053" BYTE="4" BIT="0" WIDGET_NAME="输出三相启停次数" 	/> 

    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="057" WRITE_OFFSET="057" BYTE="4" BIT="0" WIDGET_NAME="散热风扇启停次数" 	/> 
    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="061" WRITE_OFFSET="061" BYTE="4" BIT="0" WIDGET_NAME="散热风扇累计工作时间" 		READ_SCRIPT="通用_秒转分.js" />

    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="069" WRITE_OFFSET="069" BYTE="4" BIT="0" WIDGET_NAME="母线电容一40度以下" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="073" WRITE_OFFSET="073" BYTE="4" BIT="0" WIDGET_NAME="母线电容一40-50度" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="077" WRITE_OFFSET="077" BYTE="4" BIT="0" WIDGET_NAME="母线电容一50-60度" 		READ_SCRIPT="通用_秒转分.js"/>	
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="081" WRITE_OFFSET="081" BYTE="4" BIT="0" WIDGET_NAME="母线电容一60-70度"  		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="085" WRITE_OFFSET="085" BYTE="4" BIT="0" WIDGET_NAME="母线电容一70-80度"  		READ_SCRIPT="通用_秒转分.js"/>                                                                                             
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="089" WRITE_OFFSET="089" BYTE="4" BIT="0" WIDGET_NAME="母线电容一80-90度" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="093" WRITE_OFFSET="093" BYTE="4" BIT="0" WIDGET_NAME="母线电容一90-100度" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="06" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="097" WRITE_OFFSET="097" BYTE="4" BIT="0" WIDGET_NAME="母线电容一>100度" 		READ_SCRIPT="通用_秒转分.js"/>

    <CHILD WIDGET_ROW="06" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="101" WRITE_OFFSET="101" BYTE="4" BIT="0" WIDGET_NAME="母线电容二40度以下" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="06" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="105" WRITE_OFFSET="105" BYTE="4" BIT="0" WIDGET_NAME="母线电容二40-50度" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="109" WRITE_OFFSET="109" BYTE="4" BIT="0" WIDGET_NAME="母线电容二50-60度" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="113" WRITE_OFFSET="113" BYTE="4" BIT="0" WIDGET_NAME="母线电容二60-70度"  		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="117" WRITE_OFFSET="117" BYTE="4" BIT="0" WIDGET_NAME="母线电容二70-80度"  		READ_SCRIPT="通用_秒转分.js"/>                                                                                             
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="121" WRITE_OFFSET="121" BYTE="4" BIT="0" WIDGET_NAME="母线电容二80-90度" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="125" WRITE_OFFSET="125" BYTE="4" BIT="0" WIDGET_NAME="母线电容二90-100度" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="129" WRITE_OFFSET="129" BYTE="4" BIT="0" WIDGET_NAME="母线电容二>100度" 		READ_SCRIPT="通用_秒转分.js"/>

    <CHILD WIDGET_ROW="09" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="133" WRITE_OFFSET="133" BYTE="4" BIT="0" WIDGET_NAME="输出容量0-5kVA" 			READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="09" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="137" WRITE_OFFSET="137" BYTE="4" BIT="0" WIDGET_NAME="输出容量5k-10kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="09" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="141" WRITE_OFFSET="141" BYTE="4" BIT="0" WIDGET_NAME="输出容量10k-15kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="145" WRITE_OFFSET="145" BYTE="4" BIT="0" WIDGET_NAME="输出容量15k-20kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="149" WRITE_OFFSET="149" BYTE="4" BIT="0" WIDGET_NAME="输出容量20k-22kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="153" WRITE_OFFSET="153" BYTE="4" BIT="0" WIDGET_NAME="输出容量22k-24kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="157" WRITE_OFFSET="157" BYTE="4" BIT="0" WIDGET_NAME="输出容量24k-26kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="161" WRITE_OFFSET="161" BYTE="4" BIT="0" WIDGET_NAME="输出容量26k-28kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="169" WRITE_OFFSET="169" BYTE="4" BIT="0" WIDGET_NAME="输出容量28k-30kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="12" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="173" WRITE_OFFSET="173" BYTE="4" BIT="0" WIDGET_NAME="输出容量30k-32kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="12" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="177" WRITE_OFFSET="177" BYTE="4" BIT="0" WIDGET_NAME="输出容量32k-34kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="12" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="181" WRITE_OFFSET="181" BYTE="4" BIT="0" WIDGET_NAME="输出容量34k-36kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="185" WRITE_OFFSET="185" BYTE="4" BIT="0" WIDGET_NAME="输出容量36k-38kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="189" WRITE_OFFSET="189" BYTE="4" BIT="0" WIDGET_NAME="输出容量38k-40kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="193" WRITE_OFFSET="193" BYTE="4" BIT="0" WIDGET_NAME="输出容量40k-42kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="197" WRITE_OFFSET="197" BYTE="4" BIT="0" WIDGET_NAME="输出容量42k-44kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="201" WRITE_OFFSET="201" BYTE="4" BIT="0" WIDGET_NAME="输出容量44k-46kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="205" WRITE_OFFSET="205" BYTE="4" BIT="0" WIDGET_NAME="输出容量46k-48kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="209" WRITE_OFFSET="209" BYTE="4" BIT="0" WIDGET_NAME="输出容量48k-50kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="213" WRITE_OFFSET="213" BYTE="4" BIT="0" WIDGET_NAME="输出容量50k-52kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="217" WRITE_OFFSET="217" BYTE="4" BIT="0" WIDGET_NAME="输出容量52k-54kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="221" WRITE_OFFSET="221" BYTE="4" BIT="0" WIDGET_NAME="输出容量54k-56kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="225" WRITE_OFFSET="225" BYTE="4" BIT="0" WIDGET_NAME="输出容量56k-58kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="229" WRITE_OFFSET="229" BYTE="4" BIT="0" WIDGET_NAME="输出容量58k-60kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="233" WRITE_OFFSET="233" BYTE="4" BIT="0" WIDGET_NAME="输出容量60k-62kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="237" WRITE_OFFSET="237" BYTE="4" BIT="0" WIDGET_NAME="输出容量62k-64kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="241" WRITE_OFFSET="241" BYTE="4" BIT="0" WIDGET_NAME="输出容量64k-66kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="245" WRITE_OFFSET="245" BYTE="4" BIT="0" WIDGET_NAME="输出容量66k-68kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="249" WRITE_OFFSET="249" BYTE="4" BIT="0" WIDGET_NAME="输出容量68k-70kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="253" WRITE_OFFSET="253" BYTE="4" BIT="0" WIDGET_NAME="输出容量70k-72kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="257" WRITE_OFFSET="257" BYTE="4" BIT="0" WIDGET_NAME="输出容量72k-74kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="261" WRITE_OFFSET="261" BYTE="4" BIT="0" WIDGET_NAME="输出容量74k-76kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="265" WRITE_OFFSET="265" BYTE="4" BIT="0" WIDGET_NAME="输出容量76k-78kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="20" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="269" WRITE_OFFSET="269" BYTE="4" BIT="0" WIDGET_NAME="输出容量78k-80kVA" 		READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="20" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="273" WRITE_OFFSET="273" BYTE="4" BIT="0" WIDGET_NAME="输出容量>80kVA" 			READ_SCRIPT="通用_秒转分.js"/>


</IO>                                     
