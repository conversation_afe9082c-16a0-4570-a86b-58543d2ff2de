function myPadStart(str,targetLength, padString) 
{ 
	while (str.length < targetLength) 	{
		str = padString + str;
	}
	return str;
}

function fun(val)
{
	var sVal = val.toString();
	var m = sVal.slice(0,1).toString();
	var p = sVal.slice(1,3).toString();
	var f = (val % 100).toString();
	
	p = myPadStart(p,2,"0");
	f = myPadStart(f,2,"0");	

	if(m=="3")
		var ver =  'T' + m + '.' + p + "." + f;
	else if(m=="2")
		var ver =  'T' + m + '.' + p + "." + f;
	else
		var ver =  'V' + m + '.' + p+ "." + f;
	
	return ver;
}

