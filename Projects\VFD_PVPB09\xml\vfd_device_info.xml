<?xml version="1.0" encoding="UTF-8"?>
<IO>

	<!-- RTC在合并数组中的偏移, 各项目可能不同  -->
	<RTC READ_OFFSET="037" />
	
<!--系统状态   OPERATING_TEXT     -->	                                                                                                                                                  																						           
	<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="系统信息" >CONTROLLER_SYSTEM_INFO</TITLE> 
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="001" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="017" BYTE="02" BIT="0" WIDGET_NAME="软件版本" 			READ_SCRIPT="通用_主版本.js" />                                                                         
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="002" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="017" BYTE="02" BIT="0" WIDGET_NAME="修订" 				READ_SCRIPT="通用_fix.js" />						  
	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="003" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="010" BYTE="01" BIT="0" WIDGET_NAME="通信ID"		   	READ_SCRIPT="通用_HEX.js" />  
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="004" COLUMN_WIDTH="250" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="013" BYTE="04" BIT="0" WIDGET_NAME=" RTC时钟 " 		READ_SCRIPT="通用_时间戳_秒.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="008" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="027" BYTE="04" BIT="0" WIDGET_NAME="累计上电时间"   	READ_SCRIPT="通用_秒转分.js" />
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="009" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="031" BYTE="04" BIT="0" WIDGET_NAME="累计运行时间" 		READ_SCRIPT="通用_秒转分.js" />	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="007" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="023" BYTE="04" BIT="0" WIDGET_NAME="当前上电时间" 		READ_SCRIPT="通用_秒转分.js" />	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="006" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="019" BYTE="04" BIT="0" WIDGET_NAME="当前运行时间" 		READ_SCRIPT="通用_秒转分.js" />	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="041" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="105" BYTE="02" BIT="0" WIDGET_NAME="累计上电次数"	 />		
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="032" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="087" BYTE="04" BIT="0" WIDGET_NAME="累计能耗(kWh)"		READ_SCRIPT="通用_除1000_小数3.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="035" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="093" BYTE="04" BIT="0" WIDGET_NAME="当前能耗(kWh)"		READ_SCRIPT="通用_除1000_小数3.js"/>	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="033" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="091" BYTE="01" BIT="0" WIDGET_NAME="变频器型号"		READ_SCRIPT="通用_硬件型号.js" />
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="053" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="127" BYTE="01" BIT="0" WIDGET_NAME="供电方式"   	    READ_SCRIPT="通用_供电方式_读.js" />
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="005" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="009" BYTE="01" BIT="0" WIDGET_NAME="系统状态" 			READ_SCRIPT="通用_状态机.js" /> 
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="054" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="128" BYTE="01" BIT="0" WIDGET_NAME="驱动方式"   	    READ_SCRIPT="通用_驱动方式_读.js" />		
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="089" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="155" BYTE="01" BIT="0" WIDGET_NAME="风扇占空比(%)"		/>

	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="011" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="041" BYTE="02" BIT="0" WIDGET_NAME="5V电压(V)" 		READ_SCRIPT="通用_除100_小数1.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="012" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="043" BYTE="02" BIT="0" WIDGET_NAME="12V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>		
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="013" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="02" BIT="0" WIDGET_NAME="24V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="039" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="101" BYTE="02" BIT="0" WIDGET_NAME="0-10V(V)"   		READ_SCRIPT="通用_除10_小数0.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="040" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="103" BYTE="02" BIT="0" WIDGET_NAME="4-20mA(mA)"   		READ_SCRIPT="通用_除10_小数0.js"/>	

	<!-- -->
	<TITLE GROUP_ROW="1" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="电机驱动" >TEMP_INFO</TITLE>   
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="014" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="049" BYTE="02" BIT="0" WIDGET_NAME="R相输入电压(V)"   READ_SCRIPT="通用_除10_小数0.js"/>	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="015" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="051" BYTE="02" BIT="0" WIDGET_NAME="S相输入电压(V)"   READ_SCRIPT="通用_除10_小数0.js"/>	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="016" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="053" BYTE="02" BIT="0" WIDGET_NAME="T相输入电压(V)"   READ_SCRIPT="通用_除10_小数0.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="030" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="083" BYTE="02" BIT="0" WIDGET_NAME="输入容量(kVA)"		/>
	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="018" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="055" BYTE="02" BIT="0" WIDGET_NAME="R相输入电流(A)"   READ_SCRIPT="通用_除10_小数0.js"/>	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="019" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="057" BYTE="02" BIT="0" WIDGET_NAME="S相输入电流(A)"   READ_SCRIPT="通用_除10_小数0.js"/>	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="020" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="059" BYTE="02" BIT="0" WIDGET_NAME="T相输入电流(A)"   READ_SCRIPT="通用_除10_小数0.js"/>	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="030" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="083" BYTE="02" BIT="0" WIDGET_NAME="输出容量(kVA)"		/>
	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="022" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="061" BYTE="02" BIT="0" WIDGET_NAME="U相输出电压(V)"   READ_SCRIPT="通用_除10_小数0.js"/>	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="023" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="063" BYTE="02" BIT="0" WIDGET_NAME="V相输出电压(V)"   READ_SCRIPT="通用_除10_小数0.js"/>	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="024" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="065" BYTE="02" BIT="0" WIDGET_NAME="W相输出电压(V)"   READ_SCRIPT="通用_除10_小数0.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="037" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="147" BYTE="02" BIT="0" WIDGET_NAME="输入频率(Hz)"		READ_SCRIPT="通用_除10_小数1.js"/>

	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="026" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="067" BYTE="02" BIT="0" WIDGET_NAME="U相输出电流(A)"   	READ_SCRIPT="通用_除10_小数1.js"/>			 	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="027" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="069" BYTE="02" BIT="0" WIDGET_NAME="V相输出电流(A)"   	READ_SCRIPT="通用_除10_小数1.js"/>			 	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="028" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="071" BYTE="02" BIT="0" WIDGET_NAME="W相输出电流(A)"   	READ_SCRIPT="通用_除10_小数1.js"/>	
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="037" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="099" BYTE="02" BIT="0" WIDGET_NAME="输出频率(Hz)"		READ_SCRIPT="通用_除10_小数1.js"/>

	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="029" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="081" BYTE="02" BIT="0" WIDGET_NAME="直流输入电压(V)"   EAD_SCRIPT="通用_除10_小数0.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="021" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="075" BYTE="02" BIT="0" WIDGET_NAME="母线电压(V)"   	READ_SCRIPT="通用_除10_小数0.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="036" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="097" BYTE="02" BIT="0" WIDGET_NAME="逆变载波频率(kHz)"		READ_SCRIPT="通用_除10_小数1.js"/>
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="031" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="085" BYTE="02" BIT="0" WIDGET_NAME="目标转速(RPM)"		/>
	
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="034" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="092" BYTE="01" BIT="0" WIDGET_NAME="状态码"			READ_SCRIPT="通用_PMSM13_状态码读.js" />
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="025" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="077" BYTE="02" BIT="0" WIDGET_NAME="电驱故障码" />
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="017" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="079" BYTE="01" BIT="0" WIDGET_NAME="电驱状态" 		   READ_SCRIPT="通用_状态机.js"/>

		<!-- -->
	<TITLE GROUP_ROW="2" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="" >EXTRA_01</TITLE>                                
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="049" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="119" BYTE="02" BIT="0" WIDGET_NAME="共模电感温度(℃)"		READ_SCRIPT="通用_除10_小数0.js" /> 
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="048" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="117" BYTE="02" BIT="0" WIDGET_NAME="PFC电感温度(℃)" 		READ_SCRIPT="通用_除10_小数0.js" />	
 	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="047" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="115" BYTE="02" BIT="0" WIDGET_NAME="PFC模块温度(℃)" 		READ_SCRIPT="通用_除10_小数0.js" />	
                                      	                                                                                            
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="043" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="109" BYTE="02" BIT="0" WIDGET_NAME="母线电容1温度(℃)"  	READ_SCRIPT="通用_除10_小数0.js" />
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="044" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="111" BYTE="02" BIT="0" WIDGET_NAME="母线电容2温度(℃)"  	READ_SCRIPT="通用_除10_小数0.js" />
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="046" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="113" BYTE="02" BIT="0" WIDGET_NAME="逆变器模块温度(℃)" 	READ_SCRIPT="通用_除10_小数0.js" />	

	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="050" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="02" BIT="0" WIDGET_NAME="辅源整流管温度(℃)"	READ_SCRIPT="通用_除10_小数0.js" />
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="051" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="02" BIT="0" WIDGET_NAME="腔内温度(℃)"			READ_SCRIPT="通用_除10_小数0.js" />
	<CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="052" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="125" BYTE="02" BIT="0" WIDGET_NAME="腔内湿度(%)"   		 READ_SCRIPT="通用_除10_小数0.js" />

	<!-- 实时故障-->
	<TITLE GROUP_ROW="0" GROUP_COLUMN="1" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="0" GROUP_NAME="实时故障(右击清除记录)" >REALTIME_PFC_FAULT</TITLE>           
	<CHILD WIDGET_TYPE="FAULT" COLUMN_INDEX="057" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="129" BYTE="16" BIT="0" WIDGET_NAME="系统故障字" 		READ_SCRIPT="通用_故障字.js"/>
	
		<!--  -->
	<TITLE GROUP_ROW="2" GROUP_COLUMN="1" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="0" COLUMN_STRETCH="1" COLUMN_COUNT="3" GROUP_NAME="" >IO_LCD</TITLE>   	                                                                                  
	<CHILD WIDGET_TYPE="LCD6" COLUMN_INDEX="058" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="145" BYTE="02" BIT="0" WIDGET_NAME="输出电压(V)"  	DEFAULT_STR="000" />
	<CHILD WIDGET_TYPE="LCD6" COLUMN_INDEX="059" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="147" BYTE="02" BIT="0" WIDGET_NAME="输出频率(Hz)" 	DEFAULT_STR="000" 		READ_SCRIPT="通用_除100_小数2.js" />
	<CHILD WIDGET_TYPE="LCD6" COLUMN_INDEX="060" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="149" BYTE="02" BIT="0" WIDGET_NAME="电机转速(RPM)" DEFAULT_STR="000"  />		
	<!-- 数字量通道-->
	<TITLE GROUP_ROW="1" GROUP_COLUMN="1" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="数字量通道" >DI_STATE</TITLE>  
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="062" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="00" BIT="0" WIDGET_NAME="POW" />
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="069" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="00" BIT="1" WIDGET_NAME="PFC电感温度开关" 	/>
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="063" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="00" BIT="2" WIDGET_NAME="地址码1" />
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="064" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="00" BIT="3" WIDGET_NAME="地址码2" />	
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="065" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="00" BIT="4" WIDGET_NAME="输入1" 	/>
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="066" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="00" BIT="5" WIDGET_NAME="输入2" 	/>
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="067" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="00" BIT="6" WIDGET_NAME="输入3" 	/>
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="068" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="00" BIT="7" WIDGET_NAME="输入4" 	/>

	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="070" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="152" BYTE="00" BIT="0" WIDGET_NAME="母线主继电器"/>
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="071" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="152" BYTE="00" BIT="1" WIDGET_NAME="输出1" /> 
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="072" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="152" BYTE="00" BIT="2" WIDGET_NAME="输出2" /> 
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="073" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="152" BYTE="00" BIT="3" WIDGET_NAME="输出3" /> 
	<CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="075" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="152" BYTE="00" BIT="5" WIDGET_NAME="PFC逐波限流" />
	<CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="080" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="153" BYTE="00" BIT="2" WIDGET_NAME="PFC硬件过流" />
	<CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="074" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="152" BYTE="00" BIT="4" WIDGET_NAME="母线硬件过压" />

	<CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="076" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="152" BYTE="00" BIT="6" WIDGET_NAME="INV逐波限流" />
	<CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="081" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="153" BYTE="00" BIT="3" WIDGET_NAME="逆变硬件过流" />
	<CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="078" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="153" BYTE="00" BIT="1" WIDGET_NAME="PFC驱动短路故障"/>
	<CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="077" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="153" BYTE="00" BIT="0" WIDGET_NAME="INV驱动短路故障"/>
	<CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="085" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="152" BYTE="00" BIT="7" WIDGET_NAME="母线主继电器吸合故障" /> 

	<CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="079" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="153" BYTE="00" BIT="4" WIDGET_NAME="F_HD" /> 
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="082" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="153" BYTE="00" BIT="5" WIDGET_NAME="CLR_HD" /> 
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="083" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="153" BYTE="00" BIT="6" WIDGET_NAME="FS_DR" /> 
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="084" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="153" BYTE="00" BIT="7" WIDGET_NAME="FS_DR_B" /> 

	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="086" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="154" BYTE="00" BIT="0" WIDGET_NAME="485通讯" />
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="087" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="154" BYTE="00" BIT="1" WIDGET_NAME="CAN通讯" />                                                                                                			               											                                                     
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="088" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="154" BYTE="00" BIT="2" WIDGET_NAME="Flash挂载"/>	
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="087" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="154" BYTE="00" BIT="3" WIDGET_NAME="运行灯" />                                                                                                			               											                                                     
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="088" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="154" BYTE="00" BIT="4" WIDGET_NAME="故障灯"/>	
	<CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="088" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="154" BYTE="00" BIT="5" WIDGET_NAME="温湿度传感器"/>

	<!-- 
		自定义数据下发
		WRITE_OFFSET为0x81帧的写入偏移,跟上面的回读偏移无关联
		--> 
	<TITLE GROUP_ROW="3" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="2" ROW_STRETCH="1" COLUMN_STRETCH="1" GROUP_NAME="" >PUT_PARAMS</TITLE>
	<CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="24" BYTE="2" BIT="0" WIDGET_NAME="设置转速(RPM)" 		DEFAULT_VALUE ="0" />
	<CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="28" BYTE="2" BIT="0" WIDGET_NAME="设置频率(Hz)" 		DEFAULT_VALUE="50.00"  WRITE_SCRIPT="通用_乘100.js" />
	
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="13" BYTE="1" BIT="0" WIDGET_NAME="控制方式" 			SWITCH_ON_TEXT="使能" 	  	SWITCH_OFF_TEXT="使能" WRITE_SCRIPT="VFD_控制方式.js"/>
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="0" WIDGET_NAME="控制命令" 			SWITCH_ON_TEXT="EN启机" 	SWITCH_OFF_TEXT="EN启机" />

	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="7" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="使能自学习"  SWITCH_OFF_TEXT="使能自学习" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="2" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="21" BYTE="0" BIT="0" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="动态自学习" 	 SWITCH_OFF_TEXT="动态自学习" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="2" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="21" BYTE="0" BIT="1" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="静态自学习" 	 SWITCH_OFF_TEXT="静态自学习" />

	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="3" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="1" WIDGET_NAME="控制命令" 			SWITCH_ON_TEXT="使能直流" 	SWITCH_OFF_TEXT="使能直流" />
	<CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="3" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="30" BYTE="2" BIT="0" WIDGET_NAME="加热时间(S)" 		DEFAULT_VALUE="5" 	 />
	<CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="3" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="32" BYTE="2" BIT="0" WIDGET_NAME="加热电流(A)" 		DEFAULT_VALUE="2.0"  WRITE_SCRIPT="通用_乘10.js" />
	
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="4" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="2" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="使能DIO"  SWITCH_OFF_TEXT="使能DIO" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="4" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="3" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="KMON" 	 SWITCH_OFF_TEXT="KMON" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="4" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="7" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="输出1" 	 SWITCH_OFF_TEXT="输出1" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="4" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="4" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="输出2" 	 SWITCH_OFF_TEXT="输出2" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="4" WIDGET_COLUMN="4" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="6" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="输出3" 	 SWITCH_OFF_TEXT="输出3" />

	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="5" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="21" BYTE="0" BIT="6" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="TIMx风扇"  SWITCH_OFF_TEXT="TIMx风扇" />
    <CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="5" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="36" BYTE="2" BIT="0" WIDGET_NAME="占空比" 			DEFAULT_VALUE="0" 	 WIDGET_REGEXP="0|[1-9][0-9]*" />

	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="6" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="0" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="使能PWM"  SWITCH_OFF_TEXT="使能PWM" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="6" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="1" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="TIMx电机"  SWITCH_OFF_TEXT="TIMx电机" />
    <CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="6" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="26" BYTE="2" BIT="0" WIDGET_NAME="占空比" 			DEFAULT_VALUE="0" 	 WIDGET_REGEXP="0|[1-9][0-9]*" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="6" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="21" BYTE="0" BIT="3" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="TIMx升压"  SWITCH_OFF_TEXT="TIMx升压" />
    <CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="6" WIDGET_COLUMN="4" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="34" BYTE="2" BIT="0" WIDGET_NAME="占空比" 			DEFAULT_VALUE="0" 	 WIDGET_REGEXP="0|[1-9][0-9]*" />

	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="7" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="4" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="使能模块"  SWITCH_OFF_TEXT="使能模块" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="7" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="5" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="电机驱动" 	 SWITCH_OFF_TEXT="电机驱动" />
	<CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="7" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="21" BYTE="0" BIT="4" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="升压模块" 	 SWITCH_OFF_TEXT="升压模块" />
</IO>