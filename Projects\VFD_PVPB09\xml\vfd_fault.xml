<?xml version="1.0" encoding="UTF-8"?>
<IO>
<!-- 该节点的名称不可修改,固定为: VFD_FAULT -->
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="0" COLUMN_STRETCH="0" GROUP_NAME="故障" >VFD_FAULT</TITLE> 

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="009" BYTE="04" BIT="0" WIDGET_NAME="条目序号" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="150" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="013" BYTE="08" BIT="0" WIDGET_NAME="时间"   		READ_SCRIPT="通用_时间_年月日时分秒.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="020" BYTE="01" BIT="0" WIDGET_NAME="事件码" 		READ_SCRIPT="通用_日志事件码解析.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="250" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="020" BYTE="02" BIT="0" WIDGET_NAME="子事件码" 	READ_SCRIPT="通用_日志子事件码解析.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="250" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="020" BYTE="02" BIT="0" WIDGET_NAME="故障码" 		READ_SCRIPT="通用_日志诊断码解析.js" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="023" BYTE="01" BIT="0" WIDGET_NAME="设备ID" 				READ_SCRIPT="通用_HEX.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="024" BYTE="04" BIT="0" WIDGET_NAME="当前上电时间" 		READ_SCRIPT="通用_秒转分.js" />	 

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="053" BYTE="01" BIT="0" WIDGET_NAME="系统状态" 		READ_SCRIPT="通用_状态机.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="054" BYTE="01" BIT="0" WIDGET_NAME="输出状态" 		READ_SCRIPT="通用_状态机.js"	/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="055" BYTE="01" BIT="0" WIDGET_NAME="电驱状态码" 		/><!--下位机暂时没有数据没有-->

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="057" BYTE="02" BIT="0" WIDGET_NAME="电机速度(rpm)"  	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="059" BYTE="02" BIT="0" WIDGET_NAME="目标速度(rpm)"  	/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="061" BYTE="02" BIT="0" WIDGET_NAME="输出频率(Hz)"  	READ_SCRIPT="通用_除100_小数2.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="063" BYTE="04" BIT="0" WIDGET_NAME="目标频率(Hz)"  	READ_SCRIPT="通用_除100_小数2.js" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="071" BYTE="02" BIT="0" WIDGET_NAME="输入R相电压(V)"  	/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="073" BYTE="02" BIT="0" WIDGET_NAME="输入S相电压(V)"  	/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="075" BYTE="02" BIT="0" WIDGET_NAME="输入T相电压(V)"  	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="077" BYTE="02" BIT="0" WIDGET_NAME="输入R相电流(A)"  	READ_SCRIPT="通用_除10_小数1.js"/>		
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="079" BYTE="02" BIT="0" WIDGET_NAME="输入S相电流(A)"  	READ_SCRIPT="通用_除10_小数1.js"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="081" BYTE="02" BIT="0" WIDGET_NAME="输入T相电流(A)"  	READ_SCRIPT="通用_除10_小数1.js"/>		
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="083" BYTE="02" BIT="0" WIDGET_NAME="输入频率(Hz)"  	READ_SCRIPT="通用_除10_小数1.js" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="085" BYTE="02" BIT="0" WIDGET_NAME="DC输入电压(V)" 	/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="087" BYTE="01" BIT="0" WIDGET_NAME="供电方式" 		READ_SCRIPT="通用_供电方式_读.js"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="088" BYTE="01" BIT="0" WIDGET_NAME="驱动方式" 	 	READ_SCRIPT="通用_驱动方式_读.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="089" BYTE="02" BIT="0" WIDGET_NAME="直流驱动电流(A)"  READ_SCRIPT="通用_除10_小数1.js"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="091" BYTE="02" BIT="0" WIDGET_NAME="直流驱动时间(s)"  />	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="093" BYTE="02" BIT="0" WIDGET_NAME="母线电压(V)"  	/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="095" BYTE="02" BIT="0" WIDGET_NAME="输出U相电压(V)"  	READ_SCRIPT="通用_除10_小数1.js"/>		
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="097" BYTE="02" BIT="0" WIDGET_NAME="输出V相电压(V)"  	READ_SCRIPT="通用_除10_小数1.js"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="099" BYTE="02" BIT="0" WIDGET_NAME="输出W相电压(V)"  	READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="101" BYTE="02" BIT="0" WIDGET_NAME="输出U相电流(A)"  	READ_SCRIPT="通用_除10_小数1.js"/>		
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="103" BYTE="02" BIT="0" WIDGET_NAME="输出V相电流(A)"  	READ_SCRIPT="通用_除10_小数1.js"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="105" BYTE="02" BIT="0" WIDGET_NAME="输出W相电流(A)"  	READ_SCRIPT="通用_除10_小数1.js"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="107" BYTE="02" BIT="0" WIDGET_NAME="输出电压(V)"  	/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="109" BYTE="02" BIT="0" WIDGET_NAME="载波频率(kHz)"  	READ_SCRIPT="通用_除10_小数1.js"/>	


    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="033" BYTE="00" BIT="0" WIDGET_NAME="输入1" 	/>					     	  	                                                               
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="033" BYTE="00" BIT="1" WIDGET_NAME="输入2" 	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="033" BYTE="00" BIT="2" WIDGET_NAME="输入3" 	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="035" BYTE="00" BIT="5" WIDGET_NAME="输入4" 	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="033" BYTE="00" BIT="3" WIDGET_NAME="输出1" 	/> 
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="033" BYTE="00" BIT="4" WIDGET_NAME="输出2" 	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="033" BYTE="00" BIT="5" WIDGET_NAME="输出3" 	/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="033" BYTE="00" BIT="7" WIDGET_NAME="PFC电感温度开关TPSW" 	/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="034" BYTE="00" BIT="1" WIDGET_NAME="母线主继电器KMON"/>	

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="111" BYTE="02" BIT="0" WIDGET_NAME="电驱故障码"    	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="045" BYTE="08" BIT="0" WIDGET_NAME="系统故障字" 		READ_SCRIPT="通用_故障字.js"/> <!--不确定-->


    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="113" BYTE="02" BIT="0" WIDGET_NAME="3.3V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>						  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="115" BYTE="02" BIT="0" WIDGET_NAME="5V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="117" BYTE="02" BIT="0" WIDGET_NAME="12V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="119" BYTE="02" BIT="0" WIDGET_NAME="24V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>                                                                                                       					               											                   						  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="02" BIT="0" WIDGET_NAME="AUX1电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/> 
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="02" BIT="0" WIDGET_NAME="AUX2电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/> 

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="125" BYTE="01" BIT="0" WIDGET_NAME="MCU温度(℃)" 	    />   
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="126" BYTE="01" BIT="0" WIDGET_NAME="母线电容1(℃)"    	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="127" BYTE="01" BIT="0" WIDGET_NAME="母线电容2(℃)"    	/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="128" BYTE="01" BIT="0" WIDGET_NAME="共模电感(℃)"    	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="129" BYTE="01" BIT="0" WIDGET_NAME="PFC电感(℃)"    	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="130" BYTE="01" BIT="0" WIDGET_NAME="PFC模块温度(℃)"    	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="131" BYTE="01" BIT="0" WIDGET_NAME="辅助电源整流管(℃)"    />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="132" BYTE="01" BIT="0" WIDGET_NAME="腔内温度(℃)" 	/>   
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="133" BYTE="01" BIT="0" WIDGET_NAME="腔内湿度(%)"	/>   


    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="035" BYTE="00" BIT="0" WIDGET_NAME="地址码1" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="035" BYTE="00" BIT="1" WIDGET_NAME="地址码2" />										                                                   


    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="034" BYTE="00" BIT="3" WIDGET_NAME="POW"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="035" BYTE="00" BIT="4" WIDGET_NAME="PFC_CBC"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="035" BYTE="00" BIT="6" WIDGET_NAME="INV_CBC"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="034" BYTE="00" BIT="1" WIDGET_NAME="KMON_FLT"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="035" BYTE="00" BIT="2" WIDGET_NAME="I_HD_OC"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="035" BYTE="00" BIT="3" WIDGET_NAME="O_HD_OC"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="036" BYTE="00" BIT="2" WIDGET_NAME="F-IPM-PFC"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="036" BYTE="00" BIT="1" WIDGET_NAME="F-IPM-INV"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="036" BYTE="00" BIT="0" WIDGET_NAME="OVP_P_BUS"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="034" BYTE="00" BIT="5" WIDGET_NAME="F_HD"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="034" BYTE="00" BIT="4" WIDGET_NAME="CLR_HD"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="034" BYTE="00" BIT="6" WIDGET_NAME="FS_DR"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="034" BYTE="00" BIT="7" WIDGET_NAME="FS_DRB"/>


    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="00" BIT="7" WIDGET_NAME="485通讯" />		<!--偏移不确定-->			  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="046" BYTE="00" BIT="0" WIDGET_NAME="CAN通讯" />		<!--偏移不确定-->	               											                                                       

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="2" WIDGET_NAME="Flash挂载"/>	

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="039" BYTE="00" BIT="2" WIDGET_NAME="diag_lvl_1 " />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="039" BYTE="00" BIT="3" WIDGET_NAME="diag_lvl_2 " />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="039" BYTE="00" BIT="4" WIDGET_NAME="diag_lvl_3 " />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="028" BYTE="02" BIT="0" WIDGET_NAME="软件版本" 			READ_SCRIPT="通用_主版本.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="028" BYTE="02" BIT="0" WIDGET_NAME="修订" 				READ_SCRIPT="通用_fix.js" />																								           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="030" BYTE="02" BIT="0" WIDGET_NAME="硬件版本" 			READ_SCRIPT="通用_主版本.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="032" BYTE="01" BIT="0" WIDGET_NAME="型号" 				READ_SCRIPT="通用_硬件型号.js"/>


</IO>                               
















